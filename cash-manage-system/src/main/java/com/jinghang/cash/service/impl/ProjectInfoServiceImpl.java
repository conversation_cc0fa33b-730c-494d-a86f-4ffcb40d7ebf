package com.jinghang.cash.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jinghang.cash.api.vo.ProjectElementsExtVO;
import com.jinghang.cash.api.vo.ProjectElementsVO;
import com.jinghang.cash.api.vo.ProjectInfoVO;
import com.jinghang.cash.enums.AbleStatus;
import com.jinghang.cash.mapper.ProjectElementsExtMapper;
import com.jinghang.cash.mapper.ProjectElementsMapper;
import com.jinghang.cash.mapper.ProjectInfoMapper;
import com.jinghang.cash.pojo.project.ProjectElements;
import com.jinghang.cash.pojo.project.ProjectElementsExt;
import com.jinghang.cash.pojo.project.ProjectInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 项目信息服务
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:16
 */
@Service
public class ProjectInfoServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoServiceImpl.class);

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ProjectElementsMapper projectElementsMapper;

    @Autowired
    private ProjectElementsExtMapper projectElementsExtMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 项目信息缓存key前缀
     */
    private static final String PROJECT_INFO_CACHE_PREFIX = "PROJECT_INFO_";

    /**
     * 默认缓存过期时间：24 * 7小时
     */
    private static final Duration CACHE_DURATION = Duration.ofHours(24 * 7);

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    public ProjectInfoVO queryProjectInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.warn("项目编码为空，无法查询项目信息");
            return null;
        }

        // 构建缓存key
        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;

        try {
            // 1. 先从缓存中获取
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData instanceof ProjectInfoVO) {
                logger.info("从缓存中获取到项目信息，projectCode: {}", projectCode);
                return (ProjectInfoVO) cachedData;
            }

            // 2. 缓存中没有，从数据库查询
            ProjectInfoVO projectInfoVO = queryProjectInfoFromDatabase(projectCode);

            // 3. 查询结果放入缓存
            if (projectInfoVO != null) {
                Duration cacheDuration = calculateCacheDuration(projectInfoVO.getElements());
                redisTemplate.opsForValue().set(cacheKey, VO, cacheDuration.getSeconds(), TimeUnit.SECONDS);
                logger.info("项目信息已放入缓存，projectCode: {}, 缓存时长: {}秒", projectCode, cacheDuration.getSeconds());
            }

            return VO;

        } catch (Exception e) {
            logger.error("查询项目完整信息异常，projectCode: {}", projectCode, e);
            throw new RuntimeException("查询项目信息失败", e);
        }
    }

    /**
     * 从数据库查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    private ProjectInfoVO queryProjectInfoFromDatabase(String projectCode) {
        logger.info("从数据库查询项目完整信息，projectCode: {}", projectCode);

        // 1. 查询项目基本信息，只查询状态为启用的项目
        ProjectInfo projectInfo = projectInfoMapper.selectByProjectCode(projectCode);
        if (projectInfo == null || !AbleStatus.ENABLE.equals(projectInfo.getEnabled())) {
            logger.info("未找到启用状态的项目信息，projectCode: {}", projectCode);
            return null;
        }

        // 2. 转换为VO
        ProjectInfoVO projectInfoVO = new ProjectInfoVO();
        BeanUtils.copyProperties(projectInfo, projectInfoVO);
        projectInfoVO.setEnabled(projectInfo.getEnabled().name());

        // 3. 查询项目要素
        ProjectElements elements = queryProjectElements(projectCode);
        if (elements != null) {
            ProjectElementsVO elementsVO = new ProjectElementsVO();
            BeanUtil.copyProperties(elements, elementsVO);
            projectInfoVO.setElements(elementsVO);
            logger.info("查询到项目要素信息，projectCode: {}, 时效类型: {}", projectCode, elements.getProjectDurationType());
        } else {
            logger.info("未找到项目要素信息，projectCode: {}", projectCode);
        }

        // 4. 查询项目要素扩展
        ProjectElementsExt elementsExt = projectElementsExtMapper.selectByProjectCode(projectCode);
        if (elementsExt != null) {
            ProjectElementsExtVO elementsExtVO = new ProjectElementsExtVO();
            BeanUtils.copyProperties(elementsExt, elementsExtVO);
            projectInfoVO.setElementsExt(elementsExtVO);
            logger.info("查询到项目要素扩展信息，projectCode: {}", projectCode);
        } else {
            logger.info("未找到项目要素扩展信息，projectCode: {}", projectCode);
        }

        logger.info("项目完整信息查询完成，projectCode: {}", projectCode);
        return projectInfoVO;
    }

    /**
     * 查询项目要素，优先查询临时配置，如果没有或已过期则查询长期配置
     *
     * @param projectCode 项目编码
     * @return 项目要素
     */
    private ProjectElements queryProjectElements(String projectCode) {
        LocalDateTime currentTime = LocalDateTime.now();

        // 1. 先查询有效的临时项目要素
        ProjectElements temporaryElements = projectElementsMapper.selectValidTemporaryElements(
                projectCode, AbleStatus.ENABLE, "TEMPORARY", currentTime);

        if (temporaryElements != null) {
            logger.info("查询到有效的临时项目要素，projectCode: {}, 有效期: {} - {}",
                    projectCode, temporaryElements.getTempStartTime(), temporaryElements.getTempEndTime());
            return temporaryElements;
        }

        // 2. 没有有效的临时配置，查询长期配置
        ProjectElements longtimeElements = projectElementsMapper.selectByProjectCodeAndEnabledAndProjectDurationType(
                projectCode, AbleStatus.ENABLE, "LONGTIME");

        if (longtimeElements != null) {
            logger.info("查询到长期项目要素，projectCode: {}", projectCode);
            return longtimeElements;
        }

        logger.warn("未找到任何有效的项目要素配置，projectCode: {}", projectCode);
        return null;
    }

    /**
     * 计算缓存时长，如果是临时配置则使用临时配置的结束时间，否则使用默认时长
     *
     * @param elementsVO 项目要素VO
     * @return 缓存时长
     */
    private Duration calculateCacheDuration(ProjectElementsVO elementsVO) {
        if (elementsVO == null) {
            return CACHE_DURATION;
        }

        // 如果是临时配置且有结束时间，计算到结束时间的时长
        if ("TEMPORARY".equals(elementsVO.getProjectDurationType())
                && elementsVO.getTempEndTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endTime = elementsVO.getTempEndTime();

            if (endTime.isAfter(now)) {
                return Duration.between(now, endTime);
            }
        }

        // 长期配置或临时配置已过期，使用默认缓存时长
        return CACHE_DURATION;
    }


    /**
     * 清除项目信息缓存
     *
     * @param projectCode 项目编码
     */
    public void clearProjectInfoCache(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.warn("项目编码为空，无法清除缓存");
            return;
        }

        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;
        try {
            redisTemplate.delete(cacheKey);
            logger.info("已清除项目信息缓存，projectCode: {}", projectCode);
        } catch (Exception e) {
            logger.error("清除项目信息缓存异常，projectCode: {}", projectCode, e);
        }
    }
}
